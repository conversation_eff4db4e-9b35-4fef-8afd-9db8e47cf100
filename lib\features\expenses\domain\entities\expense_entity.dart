import 'package:equatable/equatable.dart';

class ExpenseEntity extends Equatable {
  final String? id;
  final String title;
  final String description;
  final double amount;
  final String categoryId;
  final DateTime date;
  final String? userId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ExpenseEntity({
    this.id,
    required this.title,
    required this.description,
    required this.amount,
    required this.categoryId,
    required this.date,
    this.userId,
    required this.createdAt,
    required this.updatedAt,
  });

  ExpenseEntity copyWith({
    String? id,
    String? title,
    String? description,
    double? amount,
    String? categoryId,
    DateTime? date,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExpenseEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      categoryId: categoryId ?? this.categoryId,
      date: date ?? this.date,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        amount,
        categoryId,
        date,
        userId,
        createdAt,
        updatedAt,
      ];
}
