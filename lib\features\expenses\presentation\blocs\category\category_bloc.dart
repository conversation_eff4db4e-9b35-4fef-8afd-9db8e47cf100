import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:expense_tracker/features/expenses/domain/entities/category_entity.dart';
import 'package:expense_tracker/features/expenses/domain/usecases/get_all_categories.dart';
import 'package:expense_tracker/core/failures/failures.dart';

part 'category_event.dart';
part 'category_state.dart';

class CategoryBloc extends Bloc<CategoryEvent, CategoryState> {
  final GetAllCategoriesUseCase getAllCategoriesUseCase;

  CategoryBloc({required this.getAllCategoriesUseCase})
    : super(CategoryInitial()) {
    on<LoadCategoriesEvent>(_onLoadCategories);
    on<RefreshCategoriesEvent>(_onRefreshCategories);
  }

  Future<void> _onLoadCategories(
    LoadCategoriesEvent event,
    Emitter<CategoryState> emit,
  ) async {
    emit(CategoryLoading());

    final result = await getAllCategoriesUseCase();

    result.fold(
      (failure) => emit(CategoryError(message: _mapFailureToMessage(failure))),
      (categories) => emit(CategoryLoaded(categories: categories)),
    );
  }

  Future<void> _onRefreshCategories(
    RefreshCategoriesEvent event,
    Emitter<CategoryState> emit,
  ) async {
    final result = await getAllCategoriesUseCase();

    result.fold(
      (failure) => emit(CategoryError(message: _mapFailureToMessage(failure))),
      (categories) => emit(CategoryLoaded(categories: categories)),
    );
  }

  String _mapFailureToMessage(failure) {
    if (failure is OfflineFailure) {
      return 'No internet connection';
    }
    return 'Something went wrong';
  }
}
