import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:expense_tracker/core/firebase/auth_service.dart';
import 'package:expense_tracker/features/expenses/data/models/expense_model.dart';

abstract class ExpenseDataSource {
  Future<List<ExpenseModel>> getAllExpenses();
  Future<List<ExpenseModel>> getExpensesByCategory(String categoryId);
  Future<List<ExpenseModel>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  );
  Future<ExpenseModel> getExpenseById(String id);
  Future<void> addExpense(ExpenseModel expense);
  Future<void> updateExpense(ExpenseModel expense);
  Future<void> deleteExpense(String id);
  Future<double> getTotalExpensesByCategory(String categoryId);
  Future<double> getTotalExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  );
}

class ExpenseDataSourceImpl implements ExpenseDataSource {
  final FirebaseFirestore firestore;
  final AuthService authService;

  ExpenseDataSourceImpl({required this.firestore, required this.authService});

  String get _userId => authService.currentUser?.uid ?? '';
  CollectionReference get _expensesCollection =>
      firestore.collection('users').doc(_userId).collection('expenses');

  @override
  Future<List<ExpenseModel>> getAllExpenses() async {
    final querySnapshot = await _expensesCollection
        .orderBy('date', descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => ExpenseModel.fromFirestore(doc))
        .toList();
  }

  @override
  Future<List<ExpenseModel>> getExpensesByCategory(String categoryId) async {
    final querySnapshot = await _expensesCollection
        .where('categoryId', isEqualTo: categoryId)
        .orderBy('date', descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => ExpenseModel.fromFirestore(doc))
        .toList();
  }

  @override
  Future<List<ExpenseModel>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final querySnapshot = await _expensesCollection
        .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
        .where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
        .orderBy('date', descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => ExpenseModel.fromFirestore(doc))
        .toList();
  }

  @override
  Future<ExpenseModel> getExpenseById(String id) async {
    final doc = await _expensesCollection.doc(id).get();
    if (!doc.exists) {
      throw Exception('Expense not found');
    }
    return ExpenseModel.fromFirestore(doc);
  }

  @override
  Future<void> addExpense(ExpenseModel expense) async {
    await _expensesCollection.add(expense.toFirestore());
  }

  @override
  Future<void> updateExpense(ExpenseModel expense) async {
    await _expensesCollection.doc(expense.id).update(expense.toFirestore());
  }

  @override
  Future<void> deleteExpense(String id) async {
    await _expensesCollection.doc(id).delete();
  }

  @override
  Future<double> getTotalExpensesByCategory(String categoryId) async {
    final querySnapshot = await _expensesCollection
        .where('categoryId', isEqualTo: categoryId)
        .get();

    double total = 0.0;
    for (final doc in querySnapshot.docs) {
      final expense = ExpenseModel.fromFirestore(doc);
      total += expense.amount;
    }
    return total;
  }

  @override
  Future<double> getTotalExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final querySnapshot = await _expensesCollection
        .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
        .where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
        .get();

    double total = 0.0;
    for (final doc in querySnapshot.docs) {
      final expense = ExpenseModel.fromFirestore(doc);
      total += expense.amount;
    }
    return total;
  }
}
