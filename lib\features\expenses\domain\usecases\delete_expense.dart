import 'package:dartz/dartz.dart';
import 'package:expense_tracker/core/failures/failures.dart';
import 'package:expense_tracker/features/expenses/domain/repositories/expense_repository.dart';

class DeleteExpenseUseCase {
  final ExpenseRepository expenseRepository;

  DeleteExpenseUseCase(this.expenseRepository);

  Future<Either<Failure, Unit>> call(String id) async {
    return await expenseRepository.deleteExpense(id);
  }
}
