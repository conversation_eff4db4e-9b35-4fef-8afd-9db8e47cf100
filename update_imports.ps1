# PowerShell script to update all import statements from todo_app to expense_tracker

# Get all Dart files recursively
$dartFiles = Get-ChildItem -Path "lib" -Recurse -Filter "*.dart"

Write-Host "Found $($dartFiles.Count) Dart files to process..."

foreach ($file in $dartFiles) {
    Write-Host "Processing: $($file.FullName)"
    
    # Read the file content
    $content = Get-Content -Path $file.FullName -Raw
    
    # Replace the package imports
    $updatedContent = $content -replace "package:todo_app/", "package:expense_tracker/"
    
    # Write back to file if there were changes
    if ($content -ne $updatedContent) {
        Set-Content -Path $file.FullName -Value $updatedContent -NoNewline
        Write-Host "  Updated imports in $($file.Name)"
    } else {
        Write-Host "  No changes needed in $($file.Name)"
    }
}

Write-Host "Import update completed!"
