import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:expense_tracker/features/expenses/domain/entities/expense_entity.dart';
import 'package:expense_tracker/features/expenses/domain/usecases/add_expense.dart';
import 'package:expense_tracker/features/expenses/domain/usecases/delete_expense.dart';
import 'package:expense_tracker/features/expenses/domain/usecases/get_all_expenses.dart';
import 'package:expense_tracker/features/expenses/domain/usecases/update_expense.dart';
import 'package:expense_tracker/core/failures/failures.dart';

part 'expense_event.dart';
part 'expense_state.dart';

class ExpenseBloc extends Bloc<ExpenseEvent, ExpenseState> {
  final GetAllExpensesUseCase getAllExpensesUseCase;
  final AddExpenseUseCase addExpenseUseCase;
  final UpdateExpenseUseCase updateExpenseUseCase;
  final DeleteExpenseUseCase deleteExpenseUseCase;

  ExpenseBloc({
    required this.getAllExpensesUseCase,
    required this.addExpenseUseCase,
    required this.updateExpenseUseCase,
    required this.deleteExpenseUseCase,
  }) : super(ExpenseInitial()) {
    on<LoadExpensesEvent>(_onLoadExpenses);
    on<AddExpenseEvent>(_onAddExpense);
    on<UpdateExpenseEvent>(_onUpdateExpense);
    on<DeleteExpenseEvent>(_onDeleteExpense);
    on<RefreshExpensesEvent>(_onRefreshExpenses);
  }

  Future<void> _onLoadExpenses(
    LoadExpensesEvent event,
    Emitter<ExpenseState> emit,
  ) async {
    emit(ExpenseLoading());

    final result = await getAllExpensesUseCase();

    result.fold(
      (failure) => emit(ExpenseError(message: _mapFailureToMessage(failure))),
      (expenses) => emit(ExpenseLoaded(expenses: expenses)),
    );
  }

  Future<void> _onAddExpense(
    AddExpenseEvent event,
    Emitter<ExpenseState> emit,
  ) async {
    if (state is ExpenseLoaded) {
      emit(ExpenseLoading());

      final result = await addExpenseUseCase(event.expense);

      result.fold(
        (failure) => emit(ExpenseError(message: _mapFailureToMessage(failure))),
        (_) => add(RefreshExpensesEvent()),
      );
    }
  }

  Future<void> _onUpdateExpense(
    UpdateExpenseEvent event,
    Emitter<ExpenseState> emit,
  ) async {
    if (state is ExpenseLoaded) {
      emit(ExpenseLoading());

      final result = await updateExpenseUseCase(event.expense);

      result.fold(
        (failure) => emit(ExpenseError(message: _mapFailureToMessage(failure))),
        (_) => add(RefreshExpensesEvent()),
      );
    }
  }

  Future<void> _onDeleteExpense(
    DeleteExpenseEvent event,
    Emitter<ExpenseState> emit,
  ) async {
    if (state is ExpenseLoaded) {
      emit(ExpenseLoading());

      final result = await deleteExpenseUseCase(event.expenseId);

      result.fold(
        (failure) => emit(ExpenseError(message: _mapFailureToMessage(failure))),
        (_) => add(RefreshExpensesEvent()),
      );
    }
  }

  Future<void> _onRefreshExpenses(
    RefreshExpensesEvent event,
    Emitter<ExpenseState> emit,
  ) async {
    final result = await getAllExpensesUseCase();

    result.fold(
      (failure) => emit(ExpenseError(message: _mapFailureToMessage(failure))),
      (expenses) => emit(ExpenseLoaded(expenses: expenses)),
    );
  }

  String _mapFailureToMessage(failure) {
    if (failure is OfflineFailure) {
      return 'No internet connection';
    }
    return 'Something went wrong';
  }
}
