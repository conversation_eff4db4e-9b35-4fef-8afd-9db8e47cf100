import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:expense_tracker/core/router/app_router.dart';
import 'package:expense_tracker/features/expenses/presentation/blocs/category/category_bloc.dart';
import 'package:expense_tracker/features/expenses/presentation/blocs/expense/expense_bloc.dart';
import 'package:expense_tracker/features/tasks/presentation/blocs/bloc/switchtheme_bloc.dart';

import 'core/themes/theme_manager.dart';

import 'package:flutter_web_plugins/flutter_web_plugins.dart';

import 'core/di/service_locator.dart' as di;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await di.init();

  setUrlStrategy(PathUrlStrategy());
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => di.sl<SwitchthemeBloc>()),
        BlocProvider(create: (context) => di.sl<ExpenseBloc>()),
        BlocProvider(create: (context) => di.sl<CategoryBloc>()),
      ],
      child: BlocBuilder<SwitchthemeBloc, SwitchthemeState>(
        builder: (context, state) {
          return MaterialApp.router(
            title: 'Expense Tracker',
            debugShowCheckedModeBanner: false,
            theme: state.themeValue
                ? AppThemes.appThemeData[AppTheme.lightTheme]
                : AppThemes.appThemeData[AppTheme.darkTheme],
            routerConfig: di.sl<AppRouter>().router,
          );
        },
      ),
    );
  }
}
