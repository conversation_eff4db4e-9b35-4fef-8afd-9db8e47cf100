import 'package:expense_tracker/core/failures/failures.dart';

// Expense Failures
class AddExpenseFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class UpdateExpenseFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class DeleteExpenseFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class GetExpensesFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class ExpenseNotFoundFailure extends Failure {
  @override
  List<Object?> get props => [];
}

// Category Failures
class AddCategoryFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class UpdateCategoryFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class DeleteCategoryFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class GetCategoriesFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class CategoryNotFoundFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class CategoryInUseFailure extends Failure {
  @override
  List<Object?> get props => [];
}

// Budget Failures
class AddBudgetFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class UpdateBudgetFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class DeleteBudgetFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class GetBudgetsFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class BudgetNotFoundFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class BudgetExceededException extends Failure {
  @override
  List<Object?> get props => [];
}
