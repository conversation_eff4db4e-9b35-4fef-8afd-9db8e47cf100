import 'package:dartz/dartz.dart';
import 'package:expense_tracker/core/failures/failures.dart';
import 'package:expense_tracker/features/expenses/domain/entities/expense_entity.dart';

abstract class ExpenseRepository {
  Future<Either<Failure, List<ExpenseEntity>>> getAllExpenses();
  Future<Either<Failure, List<ExpenseEntity>>> getExpensesByCategory(
    String categoryId,
  );
  Future<Either<Failure, List<ExpenseEntity>>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  );
  Future<Either<Failure, ExpenseEntity>> getExpenseById(String id);
  Future<Either<Failure, Unit>> addExpense(ExpenseEntity expense);
  Future<Either<Failure, Unit>> updateExpense(ExpenseEntity expense);
  Future<Either<Failure, Unit>> deleteExpense(String id);
  Future<Either<Failure, double>> getTotalExpensesByCategory(String categoryId);
  Future<Either<Failure, double>> getTotalExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  );
}
