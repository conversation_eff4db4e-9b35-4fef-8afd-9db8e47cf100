import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:get_it/get_it.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:expense_tracker/core/firebase/auth_service.dart';
import 'package:expense_tracker/core/network/network_info.dart';
import 'package:expense_tracker/core/router/app_router.dart';
import 'package:expense_tracker/features/auth/data/datasources/user_data_source.dart';
import 'package:expense_tracker/features/auth/data/repositories/user_repository_impl.dart';
import 'package:expense_tracker/features/auth/domain/repositories/user_repository.dart';
import 'package:expense_tracker/features/expenses/data/datasources/category_data_source.dart';
import 'package:expense_tracker/features/expenses/data/datasources/expense_data_source.dart';
import 'package:expense_tracker/features/expenses/data/repositories/category_repository_impl.dart';
import 'package:expense_tracker/features/expenses/data/repositories/expense_repository_impl.dart';
import 'package:expense_tracker/features/expenses/domain/repositories/category_repository.dart';
import 'package:expense_tracker/features/expenses/domain/repositories/expense_repository.dart';
import 'package:expense_tracker/features/expenses/domain/usecases/add_expense.dart';
import 'package:expense_tracker/features/expenses/domain/usecases/delete_expense.dart';
import 'package:expense_tracker/features/expenses/domain/usecases/get_all_categories.dart';
import 'package:expense_tracker/features/expenses/domain/usecases/get_all_expenses.dart';
import 'package:expense_tracker/features/expenses/domain/usecases/update_expense.dart';
import 'package:expense_tracker/features/expenses/presentation/blocs/category/category_bloc.dart';
import 'package:expense_tracker/features/expenses/presentation/blocs/expense/expense_bloc.dart';
import 'package:expense_tracker/features/tasks/presentation/blocs/bloc/switchtheme_bloc.dart';
import 'package:expense_tracker/firebase_options.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // Initialize Firebase first
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Register Firebase services
  sl.registerLazySingleton<AuthService>(
    () => AuthService(FirebaseAuth.instance),
  );
  sl.registerLazySingleton<FirebaseFirestore>(() => FirebaseFirestore.instance);

  // Core
  sl.registerLazySingleton<NetwortkInfo>(() => NetwortkInfoImpl(sl()));
  sl.registerLazySingleton(() => InternetConnectionChecker.createInstance());

  // find and instanciate AppRouter
  sl.registerLazySingleton(() => AppRouter());

  // find and instanciate SwitchthemeBloc
  sl.registerLazySingleton(() => SwitchthemeBloc());

  //Feature Auth
  //data: datasource
  // Datasources
  sl.registerLazySingleton<UserDataSource>(
    () => UserDataSourceImpl(authService: sl()),
  );
  //repository
  sl.registerLazySingleton<UserRepository>(
    () => UserRepositoryImpl(
      userDataSource: sl(),
      networkInfo: sl(),
      authService: sl(),
    ),
  );

  // Feature Expenses
  // Data sources
  sl.registerLazySingleton<ExpenseDataSource>(
    () => ExpenseDataSourceImpl(firestore: sl(), authService: sl()),
  );
  sl.registerLazySingleton<CategoryDataSource>(
    () => CategoryDataSourceImpl(firestore: sl(), authService: sl()),
  );

  // Repositories
  sl.registerLazySingleton<ExpenseRepository>(
    () => ExpenseRepositoryImpl(expenseDataSource: sl(), networkInfo: sl()),
  );
  sl.registerLazySingleton<CategoryRepository>(
    () => CategoryRepositoryImpl(categoryDataSource: sl(), networkInfo: sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => GetAllExpensesUseCase(sl()));
  sl.registerLazySingleton(() => AddExpenseUseCase(sl()));
  sl.registerLazySingleton(() => UpdateExpenseUseCase(sl()));
  sl.registerLazySingleton(() => DeleteExpenseUseCase(sl()));
  sl.registerLazySingleton(() => GetAllCategoriesUseCase(sl()));

  // BLoCs
  sl.registerFactory(
    () => ExpenseBloc(
      getAllExpensesUseCase: sl(),
      addExpenseUseCase: sl(),
      updateExpenseUseCase: sl(),
      deleteExpenseUseCase: sl(),
    ),
  );
  sl.registerFactory(() => CategoryBloc(getAllCategoriesUseCase: sl()));
}
