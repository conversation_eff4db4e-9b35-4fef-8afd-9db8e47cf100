import 'package:dartz/dartz.dart';
import 'package:expense_tracker/core/failures/failures.dart';
import 'package:expense_tracker/features/expenses/domain/entities/expense_entity.dart';
import 'package:expense_tracker/features/expenses/domain/repositories/expense_repository.dart';

class GetAllExpensesUseCase {
  final ExpenseRepository expenseRepository;

  GetAllExpensesUseCase(this.expenseRepository);

  Future<Either<Failure, List<ExpenseEntity>>> call() async {
    return await expenseRepository.getAllExpenses();
  }
}
