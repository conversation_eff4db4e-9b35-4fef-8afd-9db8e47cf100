import 'package:dartz/dartz.dart';
import 'package:expense_tracker/core/failures/failures.dart';
import 'package:expense_tracker/features/expenses/domain/entities/budget_entity.dart';

abstract class BudgetRepository {
  Future<Either<Failure, List<BudgetEntity>>> getAllBudgets();
  Future<Either<Failure, List<BudgetEntity>>> getActiveBudgets();
  Future<Either<Failure, BudgetEntity>> getBudgetById(String id);
  Future<Either<Failure, BudgetEntity?>> getBudgetByCategory(String categoryId);
  Future<Either<Failure, Unit>> addBudget(BudgetEntity budget);
  Future<Either<Failure, Unit>> updateBudget(BudgetEntity budget);
  Future<Either<Failure, Unit>> deleteBudget(String id);
  Future<Either<Failure, Unit>> updateBudgetSpent(String budgetId, double amount);
}
