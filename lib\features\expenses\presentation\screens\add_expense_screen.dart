import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:expense_tracker/features/expenses/domain/entities/expense_entity.dart';
import 'package:expense_tracker/features/expenses/presentation/blocs/expense/expense_bloc.dart';
import 'package:expense_tracker/features/expenses/presentation/blocs/category/category_bloc.dart';
import 'package:expense_tracker/features/expenses/presentation/widgets/expense_form.dart';

class AddExpenseScreen extends StatefulWidget {
  const AddExpenseScreen({super.key});

  @override
  State<AddExpenseScreen> createState() => _AddExpenseScreenState();
}

class _AddExpenseScreenState extends State<AddExpenseScreen> {
  @override
  void initState() {
    super.initState();
    context.read<CategoryBloc>().add(LoadCategoriesEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Expense'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => context.pop(),
        ),
      ),
      body: BlocListener<ExpenseBloc, ExpenseState>(
        listener: (context, state) {
          if (state is ExpenseLoaded) {
            // Expense added successfully
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Expense added successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            context.pop();
          } else if (state is ExpenseError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: BlocBuilder<CategoryBloc, CategoryState>(
          builder: (context, categoryState) {
            if (categoryState is CategoryLoading) {
              return const Center(child: CircularProgressIndicator());
            } else if (categoryState is CategoryError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(categoryState.message),
                    ElevatedButton(
                      onPressed: () {
                        context.read<CategoryBloc>().add(LoadCategoriesEvent());
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            } else if (categoryState is CategoryLoaded) {
              return ExpenseForm(
                categories: categoryState.categories,
                onSubmit: (expense) {
                  final now = DateTime.now();
                  final newExpense = ExpenseEntity(
                    title: expense.title,
                    description: expense.description,
                    amount: expense.amount,
                    categoryId: expense.categoryId,
                    date: expense.date,
                    createdAt: now,
                    updatedAt: now,
                  );

                  context.read<ExpenseBloc>().add(
                    AddExpenseEvent(expense: newExpense),
                  );
                },
              );
            }

            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}
