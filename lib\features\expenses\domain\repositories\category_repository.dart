import 'package:dartz/dartz.dart';
import 'package:expense_tracker/core/failures/failures.dart';
import 'package:expense_tracker/features/expenses/domain/entities/category_entity.dart';

abstract class CategoryRepository {
  Future<Either<Failure, List<CategoryEntity>>> getAllCategories();
  Future<Either<Failure, List<CategoryEntity>>> getDefaultCategories();
  Future<Either<Failure, CategoryEntity>> getCategoryById(String id);
  Future<Either<Failure, Unit>> addCategory(CategoryEntity category);
  Future<Either<Failure, Unit>> updateCategory(CategoryEntity category);
  Future<Either<Failure, Unit>> deleteCategory(String id);
  Future<Either<Failure, Unit>> initializeDefaultCategories();
}
