import 'package:expense_tracker/core/failures/failures.dart';

class LoginUserFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class RegisterUserFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class RegisterUserWeakPwdFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class RegisterUserUsedEmailFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class SignInUserNotFoundFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class SignInWrongPwdFailure extends Failure {
  @override
  List<Object?> get props => [];
}

class SignInFailure extends Failure {
  @override
  List<Object?> get props => [];
}
