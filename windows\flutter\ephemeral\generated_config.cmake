# Generated code do not commit.
file(TO_CMAKE_PATH "F:\\DSI3\\At_Cross_Plat\\Tools\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "F:\\DSI3\\At_Cross_Plat\\Projects\\todo_app_flutter_clean_architecture_2025-main" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=F:\\DSI3\\At_Cross_Plat\\Tools\\flutter"
  "PROJECT_DIR=F:\\DSI3\\At_Cross_Plat\\Projects\\todo_app_flutter_clean_architecture_2025-main"
  "FLUTTER_ROOT=F:\\DSI3\\At_Cross_Plat\\Tools\\flutter"
  "FLUTTER_EPHEMERAL_DIR=F:\\DSI3\\At_Cross_Plat\\Projects\\todo_app_flutter_clean_architecture_2025-main\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=F:\\DSI3\\At_Cross_Plat\\Projects\\todo_app_flutter_clean_architecture_2025-main"
  "FLUTTER_TARGET=F:\\DSI3\\At_Cross_Plat\\Projects\\todo_app_flutter_clean_architecture_2025-main\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuNA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZDY5M2I0YjlkYg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049YzI5ODA5MTM1MQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjI="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=F:\\DSI3\\At_Cross_Plat\\Projects\\todo_app_flutter_clean_architecture_2025-main\\.dart_tool\\package_config.json"
)
