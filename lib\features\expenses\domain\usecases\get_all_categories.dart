import 'package:dartz/dartz.dart';
import 'package:expense_tracker/core/failures/failures.dart';
import 'package:expense_tracker/features/expenses/domain/entities/category_entity.dart';
import 'package:expense_tracker/features/expenses/domain/repositories/category_repository.dart';

class GetAllCategoriesUseCase {
  final CategoryRepository categoryRepository;

  GetAllCategoriesUseCase(this.categoryRepository);

  Future<Either<Failure, List<CategoryEntity>>> call() async {
    return await categoryRepository.getAllCategories();
  }
}
