import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:expense_tracker/core/firebase/auth_service.dart';
import 'package:expense_tracker/features/expenses/data/models/category_model.dart';

abstract class CategoryDataSource {
  Future<List<CategoryModel>> getAllCategories();
  Future<List<CategoryModel>> getDefaultCategories();
  Future<CategoryModel> getCategoryById(String id);
  Future<void> addCategory(CategoryModel category);
  Future<void> updateCategory(CategoryModel category);
  Future<void> deleteCategory(String id);
  Future<void> initializeDefaultCategories();
}

class CategoryDataSourceImpl implements CategoryDataSource {
  final FirebaseFirestore firestore;
  final AuthService authService;

  CategoryDataSourceImpl({required this.firestore, required this.authService});

  String get _userId => authService.currentUser?.uid ?? '';
  CollectionReference get _categoriesCollection =>
      firestore.collection('users').doc(_userId).collection('categories');

  @override
  Future<List<CategoryModel>> getAllCategories() async {
    final querySnapshot = await _categoriesCollection.orderBy('name').get();

    return querySnapshot.docs
        .map((doc) => CategoryModel.fromFirestore(doc))
        .toList();
  }

  @override
  Future<List<CategoryModel>> getDefaultCategories() async {
    final querySnapshot = await _categoriesCollection
        .where('isDefault', isEqualTo: true)
        .orderBy('name')
        .get();

    return querySnapshot.docs
        .map((doc) => CategoryModel.fromFirestore(doc))
        .toList();
  }

  @override
  Future<CategoryModel> getCategoryById(String id) async {
    final doc = await _categoriesCollection.doc(id).get();
    if (!doc.exists) {
      throw Exception('Category not found');
    }
    return CategoryModel.fromFirestore(doc);
  }

  @override
  Future<void> addCategory(CategoryModel category) async {
    await _categoriesCollection.add(category.toFirestore());
  }

  @override
  Future<void> updateCategory(CategoryModel category) async {
    await _categoriesCollection.doc(category.id).update(category.toFirestore());
  }

  @override
  Future<void> deleteCategory(String id) async {
    await _categoriesCollection.doc(id).delete();
  }

  @override
  Future<void> initializeDefaultCategories() async {
    final defaultCategories = _getDefaultCategoriesData();

    for (final category in defaultCategories) {
      await _categoriesCollection.add(category.toFirestore());
    }
  }

  List<CategoryModel> _getDefaultCategoriesData() {
    final now = DateTime.now();
    return [
      CategoryModel(
        name: 'Food & Dining',
        description: 'Restaurants, groceries, and food delivery',
        icon: Icons.restaurant,
        color: Colors.orange,
        userId: _userId,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        name: 'Transportation',
        description: 'Gas, public transport, taxi, and car maintenance',
        icon: Icons.directions_car,
        color: Colors.blue,
        userId: _userId,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        name: 'Shopping',
        description: 'Clothing, electronics, and general shopping',
        icon: Icons.shopping_bag,
        color: Colors.purple,
        userId: _userId,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        name: 'Entertainment',
        description: 'Movies, games, and recreational activities',
        icon: Icons.movie,
        color: Colors.red,
        userId: _userId,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        name: 'Bills & Utilities',
        description: 'Electricity, water, internet, and phone bills',
        icon: Icons.receipt_long,
        color: Colors.green,
        userId: _userId,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        name: 'Healthcare',
        description: 'Medical expenses, pharmacy, and health insurance',
        icon: Icons.local_hospital,
        color: Colors.teal,
        userId: _userId,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}
