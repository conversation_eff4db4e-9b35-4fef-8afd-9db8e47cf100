import 'package:equatable/equatable.dart';

enum BudgetPeriod { daily, weekly, monthly, yearly }

class BudgetEntity extends Equatable {
  final String? id;
  final String name;
  final double amount;
  final double spent;
  final String? categoryId; // null means overall budget
  final BudgetPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final String? userId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const BudgetEntity({
    this.id,
    required this.name,
    required this.amount,
    this.spent = 0.0,
    this.categoryId,
    required this.period,
    required this.startDate,
    required this.endDate,
    this.userId,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  double get remaining => amount - spent;
  double get percentageUsed => spent / amount * 100;
  bool get isOverBudget => spent > amount;

  BudgetEntity copyWith({
    String? id,
    String? name,
    double? amount,
    double? spent,
    String? categoryId,
    BudgetPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BudgetEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      amount: amount ?? this.amount,
      spent: spent ?? this.spent,
      categoryId: categoryId ?? this.categoryId,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      userId: userId ?? this.userId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        amount,
        spent,
        categoryId,
        period,
        startDate,
        endDate,
        userId,
        isActive,
        createdAt,
        updatedAt,
      ];
}
