import 'package:flutter/material.dart';
import 'package:expense_tracker/features/tasks/presentation/widgets/toolbar_action_theme_widget.dart';

class TasksScreen extends StatelessWidget {
  const TasksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('TODO APP'),
        actions: [const ActionThemeButton()],
      ),
      body: const Center(child: Text('Tasks')),
    );
  }
}
