import 'package:dartz/dartz.dart';
import 'package:expense_tracker/core/failures/expense_failures.dart';
import 'package:expense_tracker/core/failures/failures.dart';
import 'package:expense_tracker/core/network/network_info.dart';
import 'package:expense_tracker/features/expenses/data/datasources/expense_data_source.dart';
import 'package:expense_tracker/features/expenses/data/models/expense_model.dart';
import 'package:expense_tracker/features/expenses/domain/entities/expense_entity.dart';
import 'package:expense_tracker/features/expenses/domain/repositories/expense_repository.dart';

class ExpenseRepositoryImpl implements ExpenseRepository {
  final ExpenseDataSource expenseDataSource;
  final NetwortkInfo networkInfo;

  ExpenseRepositoryImpl({
    required this.expenseDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<ExpenseEntity>>> getAllExpenses() async {
    if (await networkInfo.isConnected) {
      try {
        final expenses = await expenseDataSource.getAllExpenses();
        return Right(expenses);
      } catch (e) {
        return Left(GetExpensesFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, List<ExpenseEntity>>> getExpensesByCategory(
    String categoryId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final expenses = await expenseDataSource.getExpensesByCategory(
          categoryId,
        );
        return Right(expenses);
      } catch (e) {
        return Left(GetExpensesFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, List<ExpenseEntity>>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final expenses = await expenseDataSource.getExpensesByDateRange(
          startDate,
          endDate,
        );
        return Right(expenses);
      } catch (e) {
        return Left(GetExpensesFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, ExpenseEntity>> getExpenseById(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final expense = await expenseDataSource.getExpenseById(id);
        return Right(expense);
      } catch (e) {
        return Left(ExpenseNotFoundFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> addExpense(ExpenseEntity expense) async {
    if (await networkInfo.isConnected) {
      try {
        final expenseModel = ExpenseModel.fromEntity(expense);
        await expenseDataSource.addExpense(expenseModel);
        return const Right(unit);
      } catch (e) {
        return Left(AddExpenseFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> updateExpense(ExpenseEntity expense) async {
    if (await networkInfo.isConnected) {
      try {
        final expenseModel = ExpenseModel.fromEntity(expense);
        await expenseDataSource.updateExpense(expenseModel);
        return const Right(unit);
      } catch (e) {
        return Left(UpdateExpenseFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> deleteExpense(String id) async {
    if (await networkInfo.isConnected) {
      try {
        await expenseDataSource.deleteExpense(id);
        return const Right(unit);
      } catch (e) {
        return Left(DeleteExpenseFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, double>> getTotalExpensesByCategory(
    String categoryId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final total = await expenseDataSource.getTotalExpensesByCategory(
          categoryId,
        );
        return Right(total);
      } catch (e) {
        return Left(GetExpensesFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, double>> getTotalExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final total = await expenseDataSource.getTotalExpensesByDateRange(
          startDate,
          endDate,
        );
        return Right(total);
      } catch (e) {
        return Left(GetExpensesFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }
}
