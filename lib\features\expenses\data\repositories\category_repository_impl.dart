import 'package:dartz/dartz.dart';
import 'package:expense_tracker/core/failures/expense_failures.dart';
import 'package:expense_tracker/core/failures/failures.dart';
import 'package:expense_tracker/core/network/network_info.dart';
import 'package:expense_tracker/features/expenses/data/datasources/category_data_source.dart';
import 'package:expense_tracker/features/expenses/data/models/category_model.dart';
import 'package:expense_tracker/features/expenses/domain/entities/category_entity.dart';
import 'package:expense_tracker/features/expenses/domain/repositories/category_repository.dart';

class CategoryRepositoryImpl implements CategoryRepository {
  final CategoryDataSource categoryDataSource;
  final NetwortkInfo networkInfo;

  CategoryRepositoryImpl({
    required this.categoryDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<CategoryEntity>>> getAllCategories() async {
    if (await networkInfo.isConnected) {
      try {
        final categories = await categoryDataSource.getAllCategories();
        return Right(categories);
      } catch (e) {
        return Left(GetCategoriesFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, List<CategoryEntity>>> getDefaultCategories() async {
    if (await networkInfo.isConnected) {
      try {
        final categories = await categoryDataSource.getDefaultCategories();
        return Right(categories);
      } catch (e) {
        return Left(GetCategoriesFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, CategoryEntity>> getCategoryById(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final category = await categoryDataSource.getCategoryById(id);
        return Right(category);
      } catch (e) {
        return Left(CategoryNotFoundFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> addCategory(CategoryEntity category) async {
    if (await networkInfo.isConnected) {
      try {
        final categoryModel = CategoryModel.fromEntity(category);
        await categoryDataSource.addCategory(categoryModel);
        return const Right(unit);
      } catch (e) {
        return Left(AddCategoryFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> updateCategory(CategoryEntity category) async {
    if (await networkInfo.isConnected) {
      try {
        final categoryModel = CategoryModel.fromEntity(category);
        await categoryDataSource.updateCategory(categoryModel);
        return const Right(unit);
      } catch (e) {
        return Left(UpdateCategoryFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> deleteCategory(String id) async {
    if (await networkInfo.isConnected) {
      try {
        await categoryDataSource.deleteCategory(id);
        return const Right(unit);
      } catch (e) {
        return Left(DeleteCategoryFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> initializeDefaultCategories() async {
    if (await networkInfo.isConnected) {
      try {
        await categoryDataSource.initializeDefaultCategories();
        return const Right(unit);
      } catch (e) {
        return Left(AddCategoryFailure());
      }
    } else {
      return Left(OfflineFailure());
    }
  }
}
